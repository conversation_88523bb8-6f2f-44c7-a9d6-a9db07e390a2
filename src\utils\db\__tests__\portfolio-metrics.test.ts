import {
  calculatePortfolioMetrics,
  calculateDividendIncome,
  calculateRealizedGains,
  calculateTransactionCosts,
  TransactionWithAssetDetails,
  DividendData,
} from "../dashboard-queries";

// Mock data for testing
const mockTransactions: TransactionWithAssetDetails[] = [
  {
    id: "1",
    portfolio_id: "portfolio-1",
    ticker: "AAPL",
    price: 100,
    quantity: 10,
    transaction_date: "2024-01-01",
    transaction_type: "BUY",
    transaction_fee: 5,
  },
  {
    id: "2",
    portfolio_id: "portfolio-1",
    ticker: "AAPL",
    price: 120,
    quantity: 5,
    transaction_date: "2024-06-01",
    transaction_type: "SELL",
    transaction_fee: 3,
  },
  {
    id: "3",
    portfolio_id: "portfolio-1",
    ticker: "MSFT",
    price: 200,
    quantity: 5,
    transaction_date: "2024-02-01",
    transaction_type: "BUY",
    transaction_fee: 4,
  },
];

const mockAssetData = new Map([
  [
    "AAPL",
    {
      asset_id: 1,
      ticker: "AAPL",
      name: "Apple Inc.",
      company: "Apple Inc.",
      currency: { code: "USD", name: "US Dollar", symbol: "$" },
    },
  ],
  [
    "MSFT",
    {
      asset_id: 2,
      ticker: "MSFT",
      name: "Microsoft Corporation",
      company: "Microsoft Corporation",
      currency: { code: "USD", name: "US Dollar", symbol: "$" },
    },
  ],
]);

const mockLatestPrices = new Map([
  ["AAPL", 150],
  ["MSFT", 250],
]);

const mockDividends: DividendData[] = [
  {
    dividend_id: 1,
    asset_id: 1,
    ex_date: "2024-03-01",
    amount_per_share: 0.5,
    dividend_type: "Regular",
    status: "Paid",
  },
  {
    dividend_id: 2,
    asset_id: 2,
    ex_date: "2024-04-01",
    amount_per_share: 0.75,
    dividend_type: "Regular",
    status: "Paid",
  },
];

describe("Portfolio Metrics Calculations", () => {
  describe("calculateTransactionCosts", () => {
    it("should calculate total transaction costs correctly", () => {
      const result = calculateTransactionCosts(mockTransactions);
      expect(result).toBe(12); // 5 + 3 + 4
    });

    it("should handle transactions without fees", () => {
      const transactionsWithoutFees = mockTransactions.map((t) => ({
        ...t,
        transaction_fee: undefined,
      }));
      const result = calculateTransactionCosts(transactionsWithoutFees);
      expect(result).toBe(0);
    });
  });

  describe("calculateRealizedGains", () => {
    it("should calculate realized gains using FIFO method", () => {
      const result = calculateRealizedGains(
        mockTransactions,
        mockAssetData,
        "USD"
      );
      // AAPL: Bought 10 at $100, sold 5 at $120 = (120-100) * 5 = $100 gain
      expect(result).toBe(100);
    });
  });

  describe("calculateDividendIncome", () => {
    it("should calculate dividend income based on holdings at ex-date", () => {
      const result = calculateDividendIncome(
        mockTransactions,
        mockDividends,
        mockAssetData,
        "USD"
      );
      // AAPL: 10 shares * $0.5 = $5 (ex-date before sell)
      // MSFT: 5 shares * $0.75 = $3.75
      expect(result).toBe(8.75);
    });
  });

  describe("calculatePortfolioMetrics", () => {
    it("should calculate comprehensive portfolio metrics", () => {
      const result = calculatePortfolioMetrics(
        mockTransactions,
        mockLatestPrices,
        mockAssetData,
        mockDividends,
        "USD"
      );

      // Capital Investit: (5 AAPL * $100) + (5 MSFT * $200) = $1500
      expect(result.capitalInvested).toBe(1500);

      // Current Value: (5 AAPL * $150) + (5 MSFT * $250) = $2000
      // Price Gain: $2000 - $1500 = $500
      expect(result.priceGain).toBe(500);
      expect(result.priceGainPercentage).toBeCloseTo(33.33, 1);

      // Dividends: $8.75
      expect(result.dividends).toBe(8.75);

      // Realized Gain: $100
      expect(result.realizedGain).toBe(100);

      // Transaction Costs: $12
      expect(result.transactionCosts).toBe(12);

      // Total Return: $500 + $8.75 + $100 = $608.75
      expect(result.totalReturn).toBe(608.75);

      // TWRR: ($608.75 / $1500) * 100 = 40.58%
      expect(result.twrr).toBeCloseTo(40.58, 1);
    });

    it("should handle empty portfolio", () => {
      const result = calculatePortfolioMetrics(
        [],
        new Map(),
        new Map(),
        [],
        "USD"
      );

      expect(result.capitalInvested).toBe(0);
      expect(result.priceGain).toBe(0);
      expect(result.dividends).toBe(0);
      expect(result.realizedGain).toBe(0);
      expect(result.transactionCosts).toBe(0);
      expect(result.totalReturn).toBe(0);
      expect(result.twrr).toBe(0);
    });
  });
});
