"use client";

import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { usePortfolioMetrics } from "@/hooks/use-portfolio-metrics";
import { Loader2, TrendingDown, TrendingUp } from "lucide-react";
import { SupportedCurrency } from "./currency-selector";

interface PortfolioMetricsCardProps {
  selectedPortfolios: string[];
  displayCurrency: SupportedCurrency;
}

const formatCurrency = (value: number, currency: SupportedCurrency): string => {
  return new Intl.NumberFormat("ro-RO", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(value);
};

const formatPercentage = (value: number): string => {
  return `${value.toFixed(2)}%`;
};

const getPercentageColor = (value: number): string => {
  if (value > 0) return "text-green-600";
  if (value < 0) return "text-red-600";
  return "text-gray-600";
};

export function PortfolioMetricsCard({
  selectedPortfolios,
  displayCurrency,
}: PortfolioMetricsCardProps) {
  const {
    data: metrics,
    isLoading,
    error,
  } = usePortfolioMetrics(selectedPortfolios, displayCurrency);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Se încarcă datele...
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p className="text-red-500 mb-2">Eroare la încărcarea datelor</p>
              <p className="text-sm">
                {error instanceof Error ? error.message : "Eroare necunoscută"}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!metrics) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Performanță</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-64 flex items-center justify-center">
            <div className="text-center text-muted-foreground">
              <p>Nu există date de performanță disponibile</p>
              <p className="text-sm mt-2">
                Selectează portofolii cu tranzacții pentru a vedea performanța
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-xl">Performanță</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Capital Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Capital
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Capital investit
              </span>
              <span className="font-medium">
                {formatCurrency(metrics.capitalInvested, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Performance Breakdown Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Performance breakdown
          </h3>
          <div className="space-y-2">
            {/* Price gain row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Price gain
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.priceGainPercentage
                  )}`}
                >
                  {metrics.priceGainPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.priceGainPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.priceGain, displayCurrency)}
              </span>
            </div>

            {/* Dividends row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Dividends
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.dividendsPercentage
                  )}`}
                >
                  {metrics.dividendsPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.dividendsPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.dividends, displayCurrency)}
              </span>
            </div>

            {/* Realized gain row */}
            <div className="grid grid-cols-3 items-center gap-4">
              <span className="text-sm text-muted-foreground text-left">
                Realized gain
              </span>
              <div className="flex items-center justify-center gap-1">
                <span
                  className={`text-sm flex items-center gap-1 ${getPercentageColor(
                    metrics.realizedGainPercentage
                  )}`}
                >
                  {metrics.realizedGainPercentage >= 0 ? (
                    <TrendingUp className="h-4 w-4" />
                  ) : (
                    <TrendingDown className="h-4 w-4" />
                  )}
                  {formatPercentage(metrics.realizedGainPercentage)}
                </span>
              </div>
              <span className="font-medium text-right">
                {formatCurrency(metrics.realizedGain, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Transaction Costs Section */}
        <div>
          <h3 className="text-lg font-semibold text-portavio-blue mb-3">
            Transaction costs
          </h3>
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">
                Transaction costs
              </span>
              <span className="font-medium">
                {formatCurrency(metrics.transactionCosts, displayCurrency)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-sm text-muted-foreground">Taxes</span>
              <span className="font-medium">
                {formatCurrency(metrics.taxes, displayCurrency)}
              </span>
            </div>
          </div>
        </div>

        {/* Total Return Section */}
        <div className="border-t pt-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-portavio-blue">
              Total return
            </h3>
            <span className="text-xl font-bold">
              {formatCurrency(metrics.totalReturn, displayCurrency)}
            </span>
          </div>
        </div>

        {/* Performance Metrics */}
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span className="font-medium">
              True Time-Weighted Rate of Return (TWRR)
            </span>
            <span className="text-base font-semibold">
              {/* {formatPercentage(metrics.twrr)} */}
              N/A
            </span>
          </div>
          <div className="flex justify-between items-center">
            <span className="font-medium">
              Money-Weighted Rate of Return (MWRR)
            </span>
            <span className="text-base font-semibold">
              {/* {formatPercentage(metrics.mwrr)} */}
              N/A
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
